import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons';
import { Form, Modal, App, Drawer } from 'antd';
import React, { useCallback, useState, useRef, useEffect } from 'react';

// 导入重构后的模块
import type { AlertSend, AlertSendSearchParams } from '../types';
import { DEFAULT_PAGINATION } from '../constants';
import { useAlertSendData, useDrawer } from '../hooks';
import { useAlertSendTable } from '../hooks/useAlertSendTable';
import { useSelection } from '../hooks/useSelection';
import { tableStyles } from '../styles';

// 导入拆分的组件
import { createAlertSendTableColumns } from './AlertSendTableColumns';
import { AlertSendQuickSearchForm } from './forms/AlertSendSearchForm';
import { AlertSendActionButtons } from './AlertSendActionButtons';
import { AlertSendTableComponent } from './AlertSendTableComponent';
import AlertSendBasicForm from './forms/AlertSendBasicForm';
import { AlertSendService } from '../services/alertSend';

interface AlertSendTableProps {
  contentHeight?: number;
}

/**
 * 告警发送管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AlertSendTable: React.FC<AlertSendTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const { data, loading, total, pagination, loadData, refreshData, updateSearchParams, updatePagination } = useAlertSendData({ autoLoad: true });

  const { rowSelection, clearSelection, getSelectedCount, getSelectedRows } = useSelection(data, {
    crossPage: true,
    onSelectionChange: (selectedKeys: React.Key[], selectedRows: AlertSend[]) => {
      console.log('选择变化:', { selectedKeys, selectedRows });
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } = useAlertSendTable({ contentHeight });

  // 抽屉状态管理
  const { drawerState: editDrawer, showDrawer: showEditDrawer, hideDrawer: hideEditDrawer } = useDrawer();

  // 当前编辑记录
  const [currentRecord, setCurrentRecord] = useState<AlertSend | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 表单实例
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 使用 ref 来避免依赖循环
  const loadDataRef = useRef(loadData);

  useEffect(() => {
    loadDataRef.current = loadData;
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: AlertSendSearchParams) => {
      console.log('搜索参数:', values);
      updateSearchParams(values);
      updatePagination(1, pagination.page_size);
      loadDataRef.current({
        ...values,
        current: 1,
        page_size: pagination.page_size,
      });
    },
    [updateSearchParams, updatePagination, pagination.page_size]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    // 重置表单和其他状态，但不立即清空数据避免闪烁
    searchForm.resetFields();
    resetSortAndFilter();
    clearSelection();

    // 重置搜索参数和分页状态
    updateSearchParams({});
    updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);

    // 重新加载数据，明确传递空的搜索参数以确保重置生效
    loadDataRef.current({
      current: DEFAULT_PAGINATION.current,
      page_size: DEFAULT_PAGINATION.page_size,
      // 明确传递空的搜索参数，覆盖可能还未更新的状态
      name: undefined,
      receive_type: undefined,
    });
  }, [searchForm, resetSortAndFilter, clearSelection, updateSearchParams, updatePagination]);

  // 编辑处理
  const handleEdit = useCallback(
    (record: AlertSend) => {
      console.log('编辑告警发送:', record);
      setCurrentRecord(record);
      editForm.setFieldsValue(record);
      showEditDrawer({
        visible: true,
        title: '编辑告警发送',
        width: '60%',
      });
    },
    [editForm, showEditDrawer]
  );

  // 删除处理
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        console.log('删除告警发送:', id);
        await AlertSendService.deleteAlertSend(id);
        message.success('删除成功');
        await refreshData();
        clearSelection();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
    [message, refreshData, clearSelection]
  );

  // 批量删除处理
  const handleBatchDelete = useCallback(() => {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRows.length} 条告警发送配置吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const deletePromises = selectedRows.map(row => AlertSendService.deleteAlertSend(row.id));
          await Promise.all(deletePromises);
          message.success(`成功删除 ${selectedRows.length} 条记录`);
          await refreshData();
          clearSelection();
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  }, [getSelectedRows, message, refreshData, clearSelection]);

  // 表单提交处理
  const handleFormSubmit = useCallback(
    async (values: AlertSend) => {
      setSubmitLoading(true);
      try {
        console.log('提交告警发送表单:', values);

        if (currentRecord) {
          // 编辑模式
          // await TaskService.updateAlertSend(values);
          await AlertSendService.updateAlertSend(values);
          message.success('更新成功');
        } else {
          // 新增模式
          // await TaskService.addAlertSend(values);
          await AlertSendService.addAlertSend(values);
          message.success('新增成功');
        }

        hideEditDrawer();
        editForm.resetFields();
        setCurrentRecord(null);
        await refreshData();
      } catch (error) {
        console.error('提交失败:', error);
        message.error('提交失败');
      } finally {
        setSubmitLoading(false);
      }
    },
    [currentRecord, message, hideEditDrawer, editForm, refreshData]
  );

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    hideEditDrawer();
    setCurrentRecord(null);
  }, [hideEditDrawer]);

  // 取消编辑
  const handleCancel = useCallback(() => {
    hideEditDrawer();
    editForm.resetFields();
    setCurrentRecord(null);
  }, [hideEditDrawer, editForm]);

  // 表格列定义
  const columns = createAlertSendTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <AlertSendQuickSearchForm form={searchForm} onSubmit={handleSearchFormSubmit} onReset={handleReset} />

        {/* 操作按钮区域 */}
        <AlertSendActionButtons
          selectedCount={getSelectedCount()}
          onAddAlertSend={() => {
            setCurrentRecord(null);
            editForm.resetFields();
            showEditDrawer({
              visible: true,
              title: '新增告警发送',
              width: '60%',
            });
          }}
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <AlertSendTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page: number, pageSize: number) => {
            updatePagination(page, pageSize);
            loadDataRef.current({
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑告警发送' : '新增告警发送'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <AlertSendBasicForm
          form={editForm}
          initialData={currentRecord || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleCancel}
          onReset={() => {
            editForm.resetFields();
            message.info('表单已重置');
          }}
          loading={submitLoading}
        />
      </Drawer>
    </div>
  );
};

export default AlertSendTable;
