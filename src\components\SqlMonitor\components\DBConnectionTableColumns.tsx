/**
 * 数据库连接表格列配置组件
 * 用于定义数据库连接管理表格的列结构、筛选器和操作按钮
 */

import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space, Tag } from 'antd';
import type { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';
import React from 'react';

import type { DBConnection } from '../types';
import { tableStyles } from '../styles';

/**
 * 数据库连接表格列配置组件的属性接口
 */
interface DBConnectionTableColumnsProps {
  /** 表格筛选状态信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 获取指定列的排序状态 */
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  /** 编辑数据库连接的回调函数 */
  onEdit: (record: DBConnection) => void;
  /** 删除数据库连接的回调函数 */
  onDelete: (id: number) => void;
}

/**
 * 获取列搜索筛选器配置
 * 为指定列生成搜索筛选下拉框的配置对象
 */
const getColumnSearchProps = (dataIndex: string, placeholder: string, filteredInfo: Record<string, FilterValue | null>) => ({
  // 自定义筛选下拉框组件
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
    <div style={{ padding: 8 }}>
      {/* 搜索输入框 */}
      <Input
        placeholder={`搜索 ${placeholder}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={() => confirm()}
        style={{ marginBottom: 8, display: 'block' }}
      />
      {/* 操作按钮组 */}
      <Space>
        {/* 搜索按钮 */}
        <Button type="primary" onClick={() => confirm()} icon={<SearchOutlined />} size="small" style={{ width: 90 }}>
          搜索
        </Button>
        {/* 重置按钮 */}
        <Button
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          }}
          size="small"
          style={{ width: 90 }}
        >
          重置
        </Button>
      </Space>
    </div>
  ),
  // 筛选图标，根据是否有筛选条件显示不同颜色
  filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  // 筛选逻辑：模糊匹配（不区分大小写）
  onFilter: (value: React.Key | boolean, record: DBConnection) => record[dataIndex as keyof DBConnection]?.toString().toLowerCase().includes(value.toString().toLowerCase()),
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 获取列选择筛选器配置
 * 为指定列生成下拉选择筛选器的配置对象
 */
const getColumnSelectProps = (dataIndex: string, options: { text: string; value: string }[], filteredInfo: Record<string, FilterValue | null>) => ({
  // 筛选选项
  filters: options,
  // 筛选逻辑
  onFilter: (value: React.Key | boolean, record: DBConnection) => {
    const fieldValue = record[dataIndex as keyof DBConnection];
    return fieldValue?.toString().includes(value.toString());
  },
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 创建数据库连接表格列配置
 * 根据传入的参数生成完整的表格列配置数组
 */
export const createDBConnectionTableColumns = ({ filteredInfo, getSortOrder, onEdit, onDelete }: DBConnectionTableColumnsProps): TableColumnsType<DBConnection> => [
  // 连接名称列
  {
    title: '连接名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
    fixed: 'left',
    sorter: (a, b) => a.name.localeCompare(b.name),
    sortOrder: getSortOrder('name'),
    ...getColumnSearchProps('name', '连接名称', filteredInfo),
  },
  // 数据库类型列
  {
    title: '数据库类型',
    dataIndex: 'db_type',
    key: 'db_type',
    width: 120,
    sorter: (a, b) => a.db_type.localeCompare(b.db_type),
    sortOrder: getSortOrder('db_type'),
    ...getColumnSelectProps(
      'db_type',
      [
        { text: 'MySQL', value: 'mysql' },
        { text: 'Oracle', value: 'oracle' },
      ],
      filteredInfo
    ),
    render: (db_type: string) => {
      const colorMap: Record<string, string> = {
        mysql: 'blue',
        oracle: 'orange',
      };
      const labelMap: Record<string, string> = {
        mysql: 'MySQL',
        oracle: 'Oracle',
      };
      return <Tag color={colorMap[db_type] || 'default'}>{labelMap[db_type] || db_type}</Tag>;
    },
  },
  // 主机地址列
  {
    title: '主机地址',
    dataIndex: 'host',
    key: 'host',
    width: 150,
    ellipsis: true,
    sorter: (a, b) => a.host.localeCompare(b.host),
    sortOrder: getSortOrder('host'),
    ...getColumnSearchProps('host', '主机地址', filteredInfo),
  },
  // 端口列
  {
    title: '端口',
    dataIndex: 'port',
    key: 'port',
    width: 80,
    sorter: (a, b) => parseInt(a.port) - parseInt(b.port),
    sortOrder: getSortOrder('port'),
  },
  // 用户名列
  {
    title: '用户名',
    dataIndex: 'user',
    key: 'user',
    width: 120,
    ellipsis: true,
    ...getColumnSearchProps('user', '用户名', filteredInfo),
  },
  // 数据库名称列（MySQL专用）
  {
    title: '数据库名',
    dataIndex: 'database',
    key: 'database',
    width: 120,
    ellipsis: true,
    render: (database: string, record: DBConnection) => {
      if (record.db_type) {
        return database || '-';
      }
      return '-';
    },
  },
  // 实例名称列（Oracle专用）
  // {
  //   title: '实例名',
  //   dataIndex: 'instance',
  //   key: 'instance',
  //   width: 120,
  //   ellipsis: true,
  //   render: (instance: string, record: DBConnection) => {
  //     if (record.db_type === 'oracle') {
  //       return instance || '-';
  //     }
  //     return '-';
  //   },
  // },
  // SSL状态列
  // {
  //   title: 'SSL',
  //   dataIndex: 'use_ssl',
  //   key: 'use_ssl',
  //   width: 80,
  //   render: (use_ssl: boolean) => (
  //     <Tag color={use_ssl ? 'green' : 'default'}>
  //       {use_ssl ? '启用' : '禁用'}
  //     </Tag>
  //   ),
  // },
  // 操作列（编辑和删除按钮）
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    filteredValue: null,
    render: (_, record) => (
      <Space size="small">
        {/* 编辑按钮 */}
        <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEdit(record)} className={tableStyles.editButton}>
          编辑
        </Button>
        {/* 删除按钮（带确认弹窗） */}
        <Popconfirm title="确认删除" description="确定要删除这个数据库连接吗？" onConfirm={() => onDelete(record.id)} okText="确定" cancelText="取消" placement="topRight">
          <Button type="text" size="small" danger icon={<DeleteOutlined />} className={tableStyles.deleteButton}>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  },
];
